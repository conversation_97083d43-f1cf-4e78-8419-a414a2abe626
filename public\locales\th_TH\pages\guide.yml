complete:
  title: "<Nav title='ขั้นตอนทั้งหมด' linkTo='complete'/>"
  content: |
    <StepContainer linkTo='complete'>
      <StepTitle> ขั้นตอนทั้งหมด </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-user-plus' label='ลงทะเบียนเป็นสมาชิก' helpText='ระหว่างการลงทะเบียน คุณจะได้รับอีเมลยืนยันเพื่อยืนยันว่าคุณสามารถรับข้อความและทำการลงทะเบียนให้เสร็จสิ้น' />
        <StepBoxs numbering='2' icon='fa-mouse-pointer' label='ซื้อตั๋ว' helpText='คลิก "ซื้อตั๋ว" ในหน้างานที่เฉพาะเจาะจง' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='ชำระเงิน' helpText='หลังจากซื้อสำเร็จ คุณจะได้รับ QR code สำหรับตั๋วของคุณ QR code นี้สามารถได้รับผ่านอีเมลที่ลงทะเบียนไว้หรือโดยการเข้าถึงส่วน "บัญชีของฉัน" ＞ "ตั๋ว" ในเว็บไซต์ INCUTix' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='แสดง QR Code เพื่อเข้างานตรงเวลา' helpText='คลิก "บัญชีของฉัน" ไปที่ "ตั๋ว" จากนั้นแสดง "QR Code" ที่ทางเข้างาน' />
      </StepBoxsContainer>
    </StepContainer>
register:
  title: "<Nav title='ลงทะเบียนเป็นสมาชิก' linkTo='register'/>"
  content: |
    <StepContainer linkTo='register'>
      <StepTitle> ลงทะเบียนเป็นสมาชิก </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-user-plus' label='' helpText='คลิก "สมัครสมาชิก"' />
        <StepBoxs numbering='2' icon='fa-file-signature' label='' helpText='หลังจากกรอกข้อมูลส่วนตัว คลิก "ส่ง" และคลิก "รับรหัส" เพื่อรับรหัสยืนยัน รหัสยืนยันจะถูกส่งไปยังอีเมลของคุณในไม่กี่นาที (หมายเหตุ: หากคุณไม่ได้รับอีเมล กรุณาตรวจสอบโฟลเดอร์สแปม)' />
        <StepBoxs numbering='3' icon='fa-envelope' label='' helpText='หลังจากได้รับอีเมลรหัสยืนยัน จดหมายเลข 6 หลัก ไปที่หน้าเว็บและใส่หมายเลข 6 หลัก หรือคลิก "บัญชีของฉัน" และไปที่ "ข้อมูลบัญชี" จากนั้นคลิก "ยืนยันอีเมล" เพื่อรับรหัสยืนยันที่มุมขวาล่าง' />
        <StepBoxs numbering='4' icon='fa-right-to-bracket' label='' helpText='ใส่รหัสยืนยัน 6 หลักและคลิก "ยืนยัน" เพื่อทำการลงทะเบียนสมาชิกให้เสร็จสิ้น' />
      </StepBoxsContainer>
    </StepContainer>
purchase:
  title: "<Nav title='ซื้อตั๋ว' linkTo='purchase'/>"
  content: |
    <StepContainer linkTo='purchase'>
      <StepTitle> ซื้อตั๋ว </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-mouse-pointer' label='' helpText='เลือกงานที่ต้องการและคลิก "ซื้อตั๋ว"' />
        <StepBoxs numbering='2' icon='fa-calendar' label='' helpText='เลือกวันที่และรอบที่ต้องการ' />
        <StepBoxs numbering='3' icon='fa-shopping-cart' label='' helpText='เลือกประเภทตั๋วและจำนวน จากนั้นเพิ่มลงตะกร้า' />
        <StepBoxs numbering='4' icon='fa-credit-card' label='' helpText='ดำเนินการชำระเงินและรอรับ QR Code' />
      </StepBoxsContainer>
    </StepContainer>
redeem:
  title: "<Nav title='แลกรับตั๋ว' linkTo='redeem'/>"
  content: |
    <StepContainer linkTo='redeem'>
      <StepTitle> แลกรับตั๋ว </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-ticket' label='' helpText='ไปที่หน้า "แลกรับตั๋ว"' />
        <StepBoxs numbering='2' icon='fa-keyboard' label='' helpText='ใส่รหัสแลกรับที่ได้รับ' />
        <StepBoxs numbering='3' icon='fa-check' label='' helpText='คลิก "ยืนยัน" เพื่อผูกตั๋วเข้ากับบัญชีของคุณ' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='' helpText='ตั๋วจะปรากฏในส่วน "ตั๋วของฉัน" พร้อม QR Code สำหรับเข้างาน' />
      </StepBoxsContainer>
    </StepContainer>
merchandise:
  title: "<Nav title='ซื้อสินค้า' linkTo='merchandise'/>"
  content: |
    <StepContainer linkTo='merchandise'>
      <StepTitle> ซื้อสินค้า </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-mouse-pointer' label='' helpText='เข้าไปที่หน้าสินค้าเฉพาะเพื่อทำการซื้อ' />
        <StepBoxs numbering='2' icon='fa-cart-plus' label='' helpText='เลือกสินค้าและจำนวน' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='' helpText='ทำการชำระเงินออนไลน์ภายในเวลาที่กำหนด' />
        <StepBoxs numbering='4' icon='fa-thumbs-up' label='' helpText='หลังจากชำระเงินสำเร็จ คุณจะถูกนำไปยังหน้ายืนยันคำสั่งซื้อ' />
        <StepBoxs numbering='5' icon='fa-envelope' label='' helpText='คลิก "บัญชีของฉัน" ไปที่ "คำสั่งซื้อสินค้า" และทำตามคำแนะนำในอีเมลเพื่อแลกรับสินค้า' />
      </StepBoxsContainer>
    </StepContainer>
